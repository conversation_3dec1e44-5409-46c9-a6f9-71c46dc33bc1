import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppBottomNavigation extends StatelessWidget {
  final int selectedIndex;

  const AppBottomNavigation({super.key, required this.selectedIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 75,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _NavItem(
              iconPath: 'assets/icons/home.png',
              label: 'Home',
              isSelected: selectedIndex == 0,
              onTap: () => context.go('/home'),
            ),
            _NavItem(
              iconPath: 'assets/icons/diet.png',
              label: 'Meal Plans',
              isSelected: selectedIndex == 1,
              onTap: () => context.go('/meal-plans'),
            ),
            _NavItem(
              iconPath: 'assets/icons/exercise.png',
              label: 'Exercise',
              isSelected: selectedIndex == 2,
              onTap: () => context.go('/exercise'),
            ),
            _NavItem(
              iconPath: 'assets/icons/profile.png',
              label: 'Profile',
              isSelected: selectedIndex == 3,
              onTap: () => context.go('/profile'),
            ),
          ],
        ),
      ),
    );
  }
}

class _NavItem extends StatelessWidget {
  final String iconPath;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _NavItem({
    required this.iconPath,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final iconColor =
        isSelected
            ? const Color(0xFF00ADB5) // Mavi - seçili
            : const Color(0xFF3A4750); // Gri - normal

    final textColor =
        isSelected
            ? const Color(0xFF00ADB5) // Mavi - seçili
            : const Color(0xFF3A4750); // Gri - normal

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          splashColor: const Color(0xFF00ADB5).withAlpha(51),
          highlightColor: const Color(0xFF00ADB5).withAlpha(26),
          child: SizedBox(
            height: double.infinity,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ImageIcon(AssetImage(iconPath), color: iconColor, size: 24),
                  const SizedBox(height: 4),
                  Text(
                    label,
                    style: const TextStyle(
                      fontFamily: 'Montserrat',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ).copyWith(color: textColor),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
